"use strict";
/**
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.createMDXLoaderItem = exports.createMDXLoaderRule = void 0;
const loader_1 = require("./loader");
var createMDXLoader_1 = require("./createMDXLoader");
Object.defineProperty(exports, "createMDXLoaderRule", { enumerable: true, get: function () { return createMDXLoader_1.createMDXLoaderRule; } });
Object.defineProperty(exports, "createMDXLoaderItem", { enumerable: true, get: function () { return createMDXLoader_1.createMDXLoaderItem; } });
exports.default = loader_1.mdxLoader;
//# sourceMappingURL=index.js.map