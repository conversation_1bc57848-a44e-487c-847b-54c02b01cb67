{"name": "@docusaurus/plugin-sitemap", "version": "3.8.1", "description": "Simple sitemap generation plugin for Docusaurus.", "main": "lib/index.js", "types": "lib/index.d.ts", "scripts": {"build": "tsc", "watch": "tsc --watch"}, "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "https://github.com/facebook/docusaurus.git", "directory": "packages/docusaurus-plugin-sitemap"}, "license": "MIT", "dependencies": {"@docusaurus/core": "3.8.1", "@docusaurus/logger": "3.8.1", "@docusaurus/types": "3.8.1", "@docusaurus/utils": "3.8.1", "@docusaurus/utils-common": "3.8.1", "@docusaurus/utils-validation": "3.8.1", "fs-extra": "^11.1.1", "sitemap": "^7.1.1", "tslib": "^2.6.0"}, "devDependencies": {"@total-typescript/shoehorn": "^0.1.2"}, "peerDependencies": {"react": "^18.0.0 || ^19.0.0", "react-dom": "^18.0.0 || ^19.0.0"}, "engines": {"node": ">=18.0"}, "gitHead": "fa8ae13e668fcbc0481ce10c0a734e2a5b397293"}