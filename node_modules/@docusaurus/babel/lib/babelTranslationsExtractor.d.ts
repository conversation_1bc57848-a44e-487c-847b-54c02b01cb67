/**
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
import { type TransformOptions } from '@babel/core';
import type { TranslationFileContent } from '@docusaurus/types';
export type SourceCodeFileTranslations = {
    sourceCodeFilePath: string;
    translations: TranslationFileContent;
    warnings: string[];
};
export declare function extractAllSourceCodeFileTranslations(sourceCodeFilePaths: string[], babelOptions: TransformOptions): Promise<SourceCodeFileTranslations[]>;
export declare function extractSourceCodeFileTranslations(sourceCodeFilePath: string, babelOptions: TransformOptions): Promise<SourceCodeFileTranslations>;
//# sourceMappingURL=babelTranslationsExtractor.d.ts.map