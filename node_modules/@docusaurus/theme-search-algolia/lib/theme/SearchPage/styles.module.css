/**
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */

.searchQueryInput,
.searchVersionInput {
  border-radius: var(--ifm-global-radius);
  border: 2px solid var(--ifm-toc-border-color);
  font: var(--ifm-font-size-base) var(--ifm-font-family-base);
  padding: 0.8rem;
  width: 100%;
  background: var(--docsearch-searchbox-focus-background);
  color: var(--docsearch-text-color);
  margin-bottom: 0.5rem;
  transition: border var(--ifm-transition-fast) ease;
}

.searchQueryInput:focus,
.searchVersionInput:focus {
  border-color: var(--docsearch-primary-color);
  outline: none;
}

.searchQueryInput::placeholder {
  color: var(--docsearch-muted-color);
}

.searchResultsColumn {
  font-size: 0.9rem;
  font-weight: bold;
}

.algoliaLogo {
  max-width: 150px;
}

.algoliaLogoPathFill {
  fill: var(--ifm-font-color-base);
}

.searchResultItem {
  padding: 1rem 0;
  border-bottom: 1px solid var(--ifm-toc-border-color);
}

.searchResultItemHeading {
  font-weight: 400;
  margin-bottom: 0;
}

.searchResultItemPath {
  font-size: 0.8rem;
  color: var(--ifm-color-content-secondary);
  --ifm-breadcrumb-separator-size-multiplier: 1;
}

.searchResultItemSummary {
  margin: 0.5rem 0 0;
  font-style: italic;
}

@media only screen and (max-width: 996px) {
  .searchQueryColumn {
    max-width: 60% !important;
  }

  .searchVersionColumn {
    max-width: 40% !important;
  }

  .searchResultsColumn {
    max-width: 60% !important;
  }

  .searchLogoColumn {
    max-width: 40% !important;
    padding-left: 0 !important;
  }
}

@media screen and (max-width: 576px) {
  .searchQueryColumn {
    max-width: 100% !important;
  }

  .searchVersionColumn {
    max-width: 100% !important;
    padding-left: var(--ifm-spacing-horizontal) !important;
  }
}

.loadingSpinner {
  width: 3rem;
  height: 3rem;
  border: 0.4em solid #eee;
  border-top-color: var(--ifm-color-primary);
  border-radius: 50%;
  animation: loading-spin 1s linear infinite;
  margin: 0 auto;
}

@keyframes loading-spin {
  100% {
    transform: rotate(360deg);
  }
}

.loader {
  margin-top: 2rem;
}

:global(.search-result-match) {
  color: var(--docsearch-hit-color);
  background: rgb(255 215 142 / 25%);
  padding: 0.09em 0;
}
