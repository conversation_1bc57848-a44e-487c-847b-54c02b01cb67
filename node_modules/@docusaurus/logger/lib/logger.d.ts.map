{"version": 3, "file": "logger.d.ts", "sourceRoot": "", "sources": ["../src/logger.ts"], "names": [], "mappings": "AAAA;;;;;GAKG;AAGH,OAAO,KAAK,EAAC,iBAAiB,EAAC,MAAM,mBAAmB,CAAC;AAEzD,KAAK,mBAAmB,GAAG,MAAM,GAAG,MAAM,GAAG,CAAC,MAAM,GAAG,MAAM,CAAC,EAAE,CAAC;AASjE,iBAAS,WAAW,CAClB,IAAI,EAAE,oBAAoB,EAC1B,GAAG,MAAM,EAAE,mBAAmB,EAAE,GAC/B,MAAM,CAkCR;AAYD,iBAAS,IAAI,CAAC,GAAG,EAAE,OAAO,GAAG,IAAI,CAAC;AAClC,iBAAS,IAAI,CACX,GAAG,EAAE,oBAAoB,EACzB,GAAG,MAAM,EAAE,CAAC,mBAAmB,EAAE,GAAG,mBAAmB,EAAE,CAAC,GACzD,IAAI,CAAC;AAUR,iBAAS,IAAI,CAAC,GAAG,EAAE,OAAO,GAAG,IAAI,CAAC;AAClC,iBAAS,IAAI,CACX,GAAG,EAAE,oBAAoB,EACzB,GAAG,MAAM,EAAE,CAAC,mBAAmB,EAAE,GAAG,mBAAmB,EAAE,CAAC,GACzD,IAAI,CAAC;AAYR,iBAAS,KAAK,CAAC,GAAG,EAAE,OAAO,GAAG,IAAI,CAAC;AACnC,iBAAS,KAAK,CACZ,GAAG,EAAE,oBAAoB,EACzB,GAAG,MAAM,EAAE,CAAC,mBAAmB,EAAE,GAAG,mBAAmB,EAAE,CAAC,GACzD,IAAI,CAAC;AAYR,iBAAS,OAAO,CAAC,GAAG,EAAE,OAAO,GAAG,IAAI,CAAC;AACrC,iBAAS,OAAO,CACd,GAAG,EAAE,oBAAoB,EACzB,GAAG,MAAM,EAAE,CAAC,mBAAmB,EAAE,GAAG,mBAAmB,EAAE,CAAC,GACzD,IAAI,CAAC;AAuBR,iBAAS,OAAO,IAAI,IAAI,CAEvB;AAED;;;;;;;;;;;;;GAaG;AACH,iBAAS,MAAM,CAAC,iBAAiB,EAAE,iBAAiB,GAAG,OAAO,OAAO,CAepE;AAED,QAAA,MAAM,MAAM;eACC,MAAM,GAAG,MAAM,KAAG,MAAM;kBACrB,MAAM,GAAG,MAAM,KAAG,MAAM;iBACzB,MAAM,GAAG,MAAM,KAAG,MAAM;gBACzB,MAAM,GAAG,MAAM,KAAG,MAAM;gBACxB,MAAM,GAAG,MAAM,KAAG,MAAM;eACzB,MAAM,GAAG,MAAM,KAAG,MAAM;gBA1KlB,OAAO,KAAG,MAAM;eACjB,OAAO,KAAG,MAAM;gBACf,OAAO,KAAG,MAAM;gBAChB,OAAO,KAAG,MAAM;kBACd,OAAO,KAAG,MAAM;eACnB,OAAO,KAAG,MAAM;;;;;;;;CAmLjC,CAAC;AAEF,eAAe,MAAM,CAAC"}