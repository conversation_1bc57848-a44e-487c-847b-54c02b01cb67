{"name": "@docusaurus/bundler", "version": "3.8.1", "description": "Docusaurus util package to abstract the current bundler.", "main": "./lib/index.js", "types": "./lib/index.d.ts", "scripts": {"build": "tsc", "watch": "tsc --watch"}, "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "https://github.com/facebook/docusaurus.git", "directory": "packages/docusaurus-bundler"}, "license": "MIT", "dependencies": {"@babel/core": "^7.25.9", "@docusaurus/babel": "3.8.1", "@docusaurus/cssnano-preset": "3.8.1", "@docusaurus/logger": "3.8.1", "@docusaurus/types": "3.8.1", "@docusaurus/utils": "3.8.1", "babel-loader": "^9.2.1", "clean-css": "^5.3.3", "copy-webpack-plugin": "^11.0.0", "css-loader": "^6.11.0", "css-minimizer-webpack-plugin": "^5.0.1", "cssnano": "^6.1.2", "file-loader": "^6.2.0", "html-minifier-terser": "^7.2.0", "mini-css-extract-plugin": "^2.9.2", "null-loader": "^4.0.1", "postcss": "^8.5.4", "postcss-loader": "^7.3.4", "postcss-preset-env": "^10.2.1", "terser-webpack-plugin": "^5.3.9", "tslib": "^2.6.0", "url-loader": "^4.1.1", "webpack": "^5.95.0", "webpackbar": "^6.0.1"}, "peerDependencies": {"@docusaurus/faster": "*"}, "peerDependenciesMeta": {"@docusaurus/faster": {"optional": true}}, "devDependencies": {"@total-typescript/shoehorn": "^0.1.2"}, "engines": {"node": ">=18.0"}, "gitHead": "fa8ae13e668fcbc0481ce10c0a734e2a5b397293"}