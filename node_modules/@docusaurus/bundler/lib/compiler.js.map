{"version": 3, "file": "compiler.js", "sourceRoot": "", "sources": ["../src/compiler.ts"], "names": [], "mappings": ";AAAA;;;;;GAKG;;AAQH,0DAaC;AAED,gDAQC;AASD,0BAyCC;;AA9ED,wEAAwC;AACxC,mGAAmE;AAInE,SAAgB,uBAAuB,CACrC,SAA0D;IAE1D,IAAI,SAAS,EAAE,MAAM,EAAE,MAAM,EAAE,CAAC;QAC9B,yDAAyD;QACzD,2CAA2C;QAC3C,mEAAmE;QACnE,MAAM,EAAC,MAAM,EAAC,GAAG,IAAA,+BAAqB,EAAC,SAAS,CAAC,CAAC;QAClD,OAAO,MAAM;aACV,GAAG,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,gBAAM,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;aAC7B,IAAI,CAAC,OAAO,gBAAM,CAAC,MAAM,CAAC,4BAA4B,CAAC,MAAM,CAAC,CAAC;IACpE,CAAC;IACD,OAAO,SAAS,CAAC;AACnB,CAAC;AAED,SAAgB,kBAAkB,CAChC,SAA0D;IAE1D,IAAI,SAAS,EAAE,QAAQ,EAAE,MAAM,EAAE,CAAC;QAChC,SAAS,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE;YACtC,gBAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QACvB,CAAC,CAAC,CAAC;IACL,CAAC;AACH,CAAC;AASD,SAAgB,OAAO,CAAC,EACtB,OAAO,EACP,cAAc,GAIf;IACC,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;QACrC,MAAM,QAAQ,GAAG,cAAc,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;QAClD,QAAQ,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,KAAK,EAAE,EAAE;YAC1B,IAAI,GAAG,EAAE,CAAC;gBACR,gBAAM,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,IAAI,GAAG,CAAC,CAAC;gBAC/B,IAAI,GAAG,CAAC,OAAO,EAAE,CAAC;oBAChB,gBAAM,CAAC,KAAK,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;gBAC5B,CAAC;gBACD,MAAM,CAAC,GAAG,CAAC,CAAC;YACd,CAAC;YACD,oCAAoC;YACpC,MAAM,cAAc,GAAG,KAAK,EAAE,MAAM,CAAC,iBAAiB,CAAC,CAAC;YACxD,IAAI,KAAK,EAAE,SAAS,EAAE,EAAE,CAAC;gBACvB,MAAM,iBAAiB,GAAG,uBAAuB,CAAC,cAAc,CAAC,CAAC;gBAClE,MAAM,CACJ,IAAI,KAAK,CACP,6CAA6C,iBAAiB,EAAE,CACjE,CACF,CAAC;YACJ,CAAC;YACD,kBAAkB,CAAC,cAAc,CAAC,CAAC;YAEnC,sEAAsE;YACtE,0DAA0D;YAC1D,QAAQ,CAAC,KAAK,CAAC,CAAC,QAAQ,EAAE,EAAE;gBAC1B,IAAI,QAAQ,EAAE,CAAC;oBACb,gBAAM,CAAC,KAAK,CAAC,yCAAyC,QAAQ,EAAE,CAAC,CAAC;oBAClE,MAAM,CAAC,QAAQ,CAAC,CAAC;gBACnB,CAAC;qBAAM,CAAC;oBACN,OAAO,CAAC,KAAM,CAAC,CAAC;gBAClB,CAAC;YACH,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC"}