"use strict";
/**
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.createStyleLoadersFactory = exports.createJsLoaderFactory = exports.getHtmlMinifier = exports.getMinimizers = exports.registerBundlerTracing = exports.getProgressBarPlugin = exports.getCopyPlugin = exports.getCSSExtractPlugin = exports.getCurrentBundler = exports.compile = exports.formatStatsErrorMessage = exports.printStatsWarnings = void 0;
var compiler_1 = require("./compiler");
Object.defineProperty(exports, "printStatsWarnings", { enumerable: true, get: function () { return compiler_1.printStatsWarnings; } });
Object.defineProperty(exports, "formatStatsErrorMessage", { enumerable: true, get: function () { return compiler_1.formatStatsErrorMessage; } });
Object.defineProperty(exports, "compile", { enumerable: true, get: function () { return compiler_1.compile; } });
var currentBundler_1 = require("./currentBundler");
Object.defineProperty(exports, "getCurrentBundler", { enumerable: true, get: function () { return currentBundler_1.getCurrentBundler; } });
Object.defineProperty(exports, "getCSSExtractPlugin", { enumerable: true, get: function () { return currentBundler_1.getCSSExtractPlugin; } });
Object.defineProperty(exports, "getCopyPlugin", { enumerable: true, get: function () { return currentBundler_1.getCopyPlugin; } });
Object.defineProperty(exports, "getProgressBarPlugin", { enumerable: true, get: function () { return currentBundler_1.getProgressBarPlugin; } });
Object.defineProperty(exports, "registerBundlerTracing", { enumerable: true, get: function () { return currentBundler_1.registerBundlerTracing; } });
var minification_1 = require("./minification");
Object.defineProperty(exports, "getMinimizers", { enumerable: true, get: function () { return minification_1.getMinimizers; } });
var minifyHtml_1 = require("./minifyHtml");
Object.defineProperty(exports, "getHtmlMinifier", { enumerable: true, get: function () { return minifyHtml_1.getHtmlMinifier; } });
var jsLoader_1 = require("./loaders/jsLoader");
Object.defineProperty(exports, "createJsLoaderFactory", { enumerable: true, get: function () { return jsLoader_1.createJsLoaderFactory; } });
var styleLoader_1 = require("./loaders/styleLoader");
Object.defineProperty(exports, "createStyleLoadersFactory", { enumerable: true, get: function () { return styleLoader_1.createStyleLoadersFactory; } });
//# sourceMappingURL=index.js.map