"use strict";
/**
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.loadVersion = loadVersion;
const tslib_1 = require("tslib");
const path_1 = tslib_1.__importDefault(require("path"));
const lodash_1 = tslib_1.__importDefault(require("lodash"));
const utils_1 = require("@docusaurus/utils");
const utils_validation_1 = require("@docusaurus/utils-validation");
const logger_1 = tslib_1.__importDefault(require("@docusaurus/logger"));
const docs_1 = require("../docs");
const sidebars_1 = require("../sidebars");
const utils_2 = require("../sidebars/utils");
async function loadVersion({ context, options, versionMetadata, env, }) {
    const { siteDir } = context;
    async function loadVersionDocsBase(tagsFile) {
        const docFiles = await (0, docs_1.readVersionDocs)(versionMetadata, options);
        if (docFiles.length === 0) {
            throw new Error(`Docs version "${versionMetadata.versionName}" has no docs! At least one doc should exist at "${path_1.default.relative(siteDir, versionMetadata.contentPath)}".`);
        }
        function processVersionDoc(docFile) {
            return (0, docs_1.processDocMetadata)({
                docFile,
                versionMetadata,
                context,
                options,
                env,
                tagsFile,
            });
        }
        return Promise.all(docFiles.map(processVersionDoc));
    }
    async function doLoadVersion() {
        const tagsFile = await (0, utils_validation_1.getTagsFile)({
            contentPaths: versionMetadata,
            tags: options.tags,
        });
        const docsBase = await loadVersionDocsBase(tagsFile);
        // TODO we only ever need draftIds in further code, not full draft items
        // To simplify and prevent mistakes, avoid exposing draft
        // replace draft=>draftIds in content loaded
        const [drafts, docs] = lodash_1.default.partition(docsBase, (doc) => doc.draft);
        const sidebars = await (0, sidebars_1.loadSidebars)(versionMetadata.sidebarFilePath, {
            sidebarItemsGenerator: options.sidebarItemsGenerator,
            numberPrefixParser: options.numberPrefixParser,
            docs,
            drafts,
            version: versionMetadata,
            sidebarOptions: {
                sidebarCollapsed: options.sidebarCollapsed,
                sidebarCollapsible: options.sidebarCollapsible,
            },
            categoryLabelSlugger: (0, utils_1.createSlugger)(),
        });
        const sidebarsUtils = (0, utils_2.createSidebarsUtils)(sidebars);
        const docsById = (0, docs_1.createDocsByIdIndex)(docs);
        const allDocIds = Object.keys(docsById);
        sidebarsUtils.checkLegacyVersionedSidebarNames({
            sidebarFilePath: versionMetadata.sidebarFilePath,
            versionMetadata,
        });
        sidebarsUtils.checkSidebarsDocIds({
            allDocIds,
            sidebarFilePath: versionMetadata.sidebarFilePath,
            versionMetadata,
        });
        return {
            ...versionMetadata,
            docs: (0, docs_1.addDocNavigation)({
                docs,
                sidebarsUtils,
            }),
            drafts,
            sidebars,
        };
    }
    try {
        return await doLoadVersion();
    }
    catch (err) {
        logger_1.default.error `Loading of version failed for version name=${versionMetadata.versionName}`;
        throw err;
    }
}
