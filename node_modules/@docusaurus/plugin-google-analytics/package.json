{"name": "@docusaurus/plugin-google-analytics", "version": "3.8.1", "description": "Global analytics (analytics.js) plugin for Docusaurus.", "main": "lib/index.js", "types": "lib/index.d.ts", "publishConfig": {"access": "public"}, "scripts": {"build": "tsc --build", "watch": "tsc --build --watch"}, "repository": {"type": "git", "url": "https://github.com/facebook/docusaurus.git", "directory": "packages/docusaurus-plugin-google-analytics"}, "license": "MIT", "dependencies": {"@docusaurus/core": "3.8.1", "@docusaurus/types": "3.8.1", "@docusaurus/utils-validation": "3.8.1", "tslib": "^2.6.0"}, "peerDependencies": {"react": "^18.0.0 || ^19.0.0", "react-dom": "^18.0.0 || ^19.0.0"}, "engines": {"node": ">=18.0"}, "gitHead": "fa8ae13e668fcbc0481ce10c0a734e2a5b397293"}