{"name": "@slorber/remark-comment", "version": "1.0.0", "description": "Remark plugin to support comments", "author": "<PERSON> <<EMAIL>> (https://leebyron.com)", "license": "MIT", "keywords": ["unified", "remark", "remark-plugin", "plugin", "mdast", "markdown", "comment"], "repository": "https://github.com/leebyron/remark-comment", "sideEffects": false, "type": "module", "main": "index.js", "types": "index.d.ts", "files": ["index.js", "index.d.ts"], "dependencies": {"micromark-factory-space": "^1.0.0", "micromark-util-character": "^1.1.0", "micromark-util-symbol": "^1.0.1"}, "scripts": {"test": "cd test && npm install --no-package-lock && npm test"}}