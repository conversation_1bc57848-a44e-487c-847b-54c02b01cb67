import type { PluginCreator } from 'postcss';

/** postcss-hwb-function plugin options */
export declare type basePluginOptions = {
    /** Preserve the original notation. default: false */
    preserve?: boolean;
};

/** postcss-hwb-function plugin options */
export declare type pluginOptions = {
    /** Preserve the original notation. default: false */
    preserve?: boolean;
    /** Enable "@csstools/postcss-progressive-custom-properties". default: true */
    enableProgressiveCustomProperties?: boolean;
};

/** Transform hwb() functions in CSS. */
declare const postcssPlugin: PluginCreator<pluginOptions>;
export default postcssPlugin;

export { }
