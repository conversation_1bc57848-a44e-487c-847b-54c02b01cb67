"use strict";var s=require("@csstools/postcss-progressive-custom-properties"),e=require("postcss-value-parser"),r=require("@csstools/utilities");const t=/ic\b/i,o=/\(font-size: \d+ic\)/i,basePlugin=s=>({postcssPlugin:"postcss-ic-unit",Declaration(i){if(!t.test(i.value))return;if(r.hasFallback(i))return;if(r.hasSupportsAtRuleAncestor(i,o))return;const u=e(i.value);u.walk((s=>{if(!s.type||"word"!==s.type)return;const r=e.unit(s.value);r&&"ic"===r.unit.toLowerCase()&&(s.value=`${r.number}em`)}));const c=String(u);c!==i.value&&(i.cloneBefore({value:c}),s?.preserve||i.remove())}});basePlugin.postcss=!0;const postcssPlugin=e=>{const r=Object.assign({preserve:!1,enableProgressiveCustomProperties:!0},e);return r.enableProgressiveCustomProperties&&r.preserve?{postcssPlugin:"postcss-ic-unit",plugins:[s(),basePlugin(r)]}:basePlugin(r)};postcssPlugin.postcss=!0,module.exports=postcssPlugin;
