"use strict";var e=require("@csstools/css-parser-algorithms"),i=require("@csstools/css-tokenizer"),n=require("@csstools/media-query-list-parser"),a=require("@csstools/css-calc");const t={width:"px",height:"px","device-width":"px","device-height":"px","aspect-ratio":"","device-aspect-ratio":"",color:"","color-index":"",monochrome:"",resolution:"dpi"},r={width:!1,height:!1,"device-width":!1,"device-height":!1,"aspect-ratio":!1,"device-aspect-ratio":!1,color:!0,"color-index":!0,monochrome:!0,resolution:"dpi"};function featureNamePrefix(e){return e===n.MediaFeatureLT.LT||e===n.MediaFeatureLT.LT_OR_EQ?"max-":e===n.MediaFeatureGT.GT||e===n.MediaFeatureGT.GT_OR_EQ?"min-":""}const o={">":1,"<":-1},s=.001;function transformSingleNameValuePair(u,d,l,c){let p=l.before,m=l.after;if(c||(p=l.after,m=l.before),!c){const e=n.invertComparison(d);if(!1===e)return;d=e}if(d===n.MediaFeatureEQ.EQ||d===n.MediaFeatureLT.LT_OR_EQ||d===n.MediaFeatureGT.GT_OR_EQ)return Array.isArray(l.value)?n.newMediaFeaturePlain(featureNamePrefix(d)+u,...p,...l.value.flatMap((e=>e.tokens())),...m):n.newMediaFeaturePlain(featureNamePrefix(d)+u,...p,...l.value.tokens(),...m);let T,f,v=!1;if(Array.isArray(l.value)){if(!n.matchesRatioExactly(l.value))return;if("aspect-ratio"!==u&&"device-aspect-ratio"!==u)return;const e=n.matchesRatio(l.value);if(-1===e)return;v=!0,T=l.value[e[0]],f=[...l.value.slice(e[0]+1).flatMap((e=>e.tokens()))]}else T=l.value,f=[];const y=t[u.toLowerCase()];if(e.isFunctionNode(T)){const t=T.getName().toLowerCase();if(a.mathFunctionNames.has(t)||"env"===t){const[[t]]=a.calcFromComponentValues([[T]],{precision:5,toCanonicalUnits:!0});if(!(t&&e.isTokenNode(t)&&i.isTokenNumeric(t.value)&&Number.isInteger(t.value[4].value))){let e;if(void 0!==y){const n=o[d]*("px"===y?.02:s);e=[i.TokenType.Dimension,`${n.toString()}${y}`,-1,-1,{value:n,unit:y,type:i.NumberType.Integer}]}else if(!0===r[u]){const n=o[d];e=[i.TokenType.Number,n.toString(),-1,-1,{value:n,type:i.NumberType.Integer}]}else if(v){const n=o[d]*s;e=[i.TokenType.Number,n.toString(),-1,-1,{value:n,type:i.NumberType.Integer}]}else{const n=o[d];e=[i.TokenType.Number,n.toString(),-1,-1,{value:n,type:i.NumberType.Integer}]}return n.newMediaFeaturePlain(featureNamePrefix(d)+u,...p,[i.TokenType.Function,"calc(",-1,-1,{value:"calc("}],[i.TokenType.OpenParen,"(",-1,-1,void 0],...T.tokens().slice(1),[i.TokenType.Whitespace," ",-1,-1,void 0],[i.TokenType.Delim,"+",-1,-1,{value:"+"}],[i.TokenType.Whitespace," ",-1,-1,void 0],e,[i.TokenType.CloseParen,")",-1,-1,void 0],...f,...m)}T=t}}if(!e.isTokenNode(T))return;let h,M=T.value,g="";if(void 0!==y&&i.isTokenNumber(M)&&0===M[4].value)h=o[d],g=y;else if(i.isTokenNumber(M)&&0===M[4].value)h=o[d],g="";else if(i.isTokenDimension(M)&&0===M[4].value)h=o[d],g=M[4].unit;else if(i.isTokenNumber(M)&&!0===r[u])h=M[4].value+o[d];else if(i.isTokenDimension(M)&&"px"===M[4].unit&&M[4].type===i.NumberType.Integer)h=Number(Math.round(Number(M[4].value+.02*o[d]+"e6"))+"e-6");else{if(!i.isTokenDimension(M)&&!i.isTokenNumber(M))return;h=Number(Math.round(Number(M[4].value+s*o[d]+"e6"))+"e-6")}return g&&(M=[i.TokenType.Dimension,M[1],M[2],M[3],{value:M[4].value,unit:g,type:M[4].type}]),M[4].value=h,i.isTokenDimension(M)?M[1]=M[4].value.toString()+M[4].unit:M[1]=M[4].value.toString(),n.newMediaFeaturePlain(featureNamePrefix(d)+u,...p,M,...f,...m)}const u=new Set(["aspect-ratio","color","color-index","device-aspect-ratio","device-height","device-width","height","horizontal-viewport-segments","monochrome","resolution","vertical-viewport-segments","width"]);function transform(a){return a.map(((a,t)=>{const r=e.gatherNodeAncestry(a);a.walk((e=>{const t=e.node;if(!n.isMediaFeatureRange(t))return;const o=e.parent;if(!n.isMediaFeature(o))return;const s=t.name.getName().toLowerCase();if(!u.has(s))return;if(n.isMediaFeatureRangeNameValue(t)||n.isMediaFeatureRangeValueName(t)){const e=t.operatorKind();if(!1===e)return;const i=transformSingleNameValuePair(s,e,t.value,n.isMediaFeatureRangeNameValue(t));return void(i&&(o.feature=i.feature))}const d=r.get(o);if(!n.isMediaInParens(d))return;let l=null,c=null;{const e=t.valueOneOperatorKind();if(!1===e)return;const i=transformSingleNameValuePair(s,e,t.valueOne,!1);if(!i)return;e===n.MediaFeatureLT.LT||e===n.MediaFeatureLT.LT_OR_EQ?(l=i,l.before=o.before):(c=i,c.after=o.after)}{const e=t.valueTwoOperatorKind();if(!1===e)return;const i=transformSingleNameValuePair(s,e,t.valueTwo,!0);if(!i)return;e===n.MediaFeatureLT.LT||e===n.MediaFeatureLT.LT_OR_EQ?(c=i,c.before=o.before):(l=i,l.after=o.after)}if(!l||!c)return;const p=new n.MediaInParens(l),m=new n.MediaInParens(c),T=getMediaConditionListWithAndFromAncestry(d,r);if(T)return T.leading===d?(T.leading=p,void(T.list=[new n.MediaAnd([[i.TokenType.Whitespace," ",-1,-1,void 0],[i.TokenType.Ident,"and",-1,-1,{value:"and"}],[i.TokenType.Whitespace," ",-1,-1,void 0]],m),...T.list])):void T.list.splice(T.indexOf(r.get(d)),1,new n.MediaAnd([[i.TokenType.Whitespace," ",-1,-1,void 0],[i.TokenType.Ident,"and",-1,-1,{value:"and"}],[i.TokenType.Whitespace," ",-1,-1,void 0]],p),new n.MediaAnd([[i.TokenType.Whitespace," ",-1,-1,void 0],[i.TokenType.Ident,"and",-1,-1,{value:"and"}],[i.TokenType.Whitespace," ",-1,-1,void 0]],m));const f=new n.MediaConditionListWithAnd(p,[new n.MediaAnd([[i.TokenType.Whitespace," ",-1,-1,void 0],[i.TokenType.Ident,"and",-1,-1,{value:"and"}],[i.TokenType.Whitespace," ",-1,-1,void 0]],m)],[[i.TokenType.Whitespace," ",-1,-1,void 0]]),v=getMediaConditionInShallowMediaQueryFromAncestry(d,a,r);v?v.media=f:d.media=new n.MediaCondition(new n.MediaInParens(new n.MediaCondition(f),[[i.TokenType.Whitespace," ",-1,-1,void 0],[i.TokenType.OpenParen,"(",-1,-1,void 0]],[[i.TokenType.CloseParen,")",-1,-1,void 0]]))}));const o=a.tokens();return i.stringify(...o.filter(((e,n)=>(0!==n||0!==t||!i.isTokenWhitespace(e))&&!(i.isTokenWhitespace(e)&&o[n+1]&&i.isTokenWhitespace(o[n+1])))))})).join(",")}function getMediaConditionListWithAndFromAncestry(e,i){let a=e;if(a){if(a=i.get(a),n.isMediaConditionListWithAnd(a))return a;if(n.isMediaAnd(a))return a=i.get(a),n.isMediaConditionListWithAnd(a)?a:void 0}}function getMediaConditionInShallowMediaQueryFromAncestry(e,i,a){let t=e;if(!t)return;if(t=a.get(t),!n.isMediaCondition(t))return;const r=t;return t=a.get(t),n.isMediaQuery(t)&&t===i?r:void 0}const creator=()=>({postcssPlugin:"postcss-media-minmax",AtRule:{media(e){if(!(e.params.includes("<")||e.params.includes(">")||e.params.includes("=")))return;const i=transform(n.parse(e.params,{preserveInvalidMediaQueries:!0,onParseError:()=>{throw e.error(`Unable to parse media query "${e.params}"`)}}));e.params!==i&&(e.params=i)},"custom-media"(e){if(!(e.params.includes("<")||e.params.includes(">")||e.params.includes("=")))return;const a=n.parseCustomMedia(e.params,{preserveInvalidMediaQueries:!0,onParseError:()=>{throw e.error(`Unable to parse media query "${e.params}"`)}});if(!a||!a.mediaQueryList)return;const t=a.mediaQueryList.map((e=>e.toString())).join(","),r=transform(a.mediaQueryList);t!==r&&(e.params=`${i.stringify(...a.name)} ${r}`)}}});creator.postcss=!0,module.exports=creator;
