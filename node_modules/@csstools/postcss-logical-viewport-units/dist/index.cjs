"use strict";var t,e=require("@csstools/utilities"),o=require("@csstools/css-tokenizer");function transform(t,e){const i=o.tokenizer({css:t}),n=[];let s=!1;for(;;){const t=i.nextToken();if(!t)break;if(n.push(t),o.isTokenEOF(t))break;if(!o.isTokenDimension(t))continue;const r=t[4].unit.toLowerCase();let c;"vi"===r?c=e.vi:"vb"===r&&(c=e.vb),c&&(t[1]=t[4].value.toString()+c,t[4].unit=c,s=!0)}return s?o.stringify(...n):t}!function(t){t.TopToBottom="top-to-bottom",t.BottomToTop="bottom-to-top",t.RightToLeft="right-to-left",t.LeftToRight="left-to-right"}(t||(t={}));const i=/vb|vi/i,n={test(t){if(!i.test(t))return!1;const e=o.tokenizer({css:t});for(;;){const t=e.nextToken();if(o.isTokenEOF(t))break;if(!o.isTokenDimension(t))continue;const i=t[4].unit.toLowerCase();if("vb"===i||"vi"===i)return!0}return!1}},s=/(?:vi|vb)\b/i,creator=o=>{const i=Object.assign({inlineDirection:t.LeftToRight,preserve:!0},o);switch(i.inlineDirection){case t.LeftToRight:case t.RightToLeft:case t.TopToBottom:case t.BottomToTop:break;default:throw new Error(`[postcss-logical-viewport-units] "inlineDirection" must be one of ${Object.values(t).join(", ")}`)}const r=[t.LeftToRight,t.RightToLeft].includes(i.inlineDirection),c={vb:"vh",vi:"vw"};return r||(c.vb="vw",c.vi="vh"),{postcssPlugin:"postcss-logical-viewport-units",Declaration(t,{atRule:o}){if(!s.test(t.value))return;if(e.hasFallback(t))return;if(e.hasSupportsAtRuleAncestor(t,n))return;const r=transform(t.value,c);if(r===t.value)return;if(t.cloneBefore({value:r}),!i.preserve)return void t.remove();if(!t.variable)return;const u=o({name:"supports",params:"(top: 1vi)",source:t.source}),a=t.parent;if(!a)return;const f=a.cloneAfter({nodes:[]});f.append(t),u.append(f),a.after(u)}}};creator.postcss=!0,module.exports=creator;
