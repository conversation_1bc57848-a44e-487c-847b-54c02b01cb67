"use strict";var e=require("@csstools/css-calc");const r=String.fromCodePoint(0);function randomCacheKeyFromPostcssDeclaration(e){let o="",s=e.parent;for(;s;){switch(s.type){case"rule":o+=r+"selector"+r+s.selector+r;break;case"atrule":"scope"===s.name&&(o+=r+"prelude"+r+s.params+r)}s=s.parent}return{propertyName:e.prop,propertyN:0,elementID:o,documentID:e.source?.input.css??e.root().toString()}}const o=/(?<![-\w])(?:random)\(/i,creator=r=>{const s=Object.assign({preserve:!1},r);return{postcssPlugin:"postcss-random-function",Declaration(r){if(!o.test(r.value))return;const t=e.calc(r.value,{precision:5,toCanonicalUnits:!0,randomCaching:randomCacheKeyFromPostcssDeclaration(r)});t!==r.value&&(r.cloneBefore({value:t}),s.preserve||r.remove())}}};creator.postcss=!0,module.exports=creator;
