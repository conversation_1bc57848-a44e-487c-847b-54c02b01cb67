"use strict";var e=require("@csstools/media-query-list-parser"),r=require("@csstools/css-parser-algorithms"),n=require("@csstools/css-tokenizer");const o=1e5,t=2147483647;function transformMediaFeatureValue(a){if(Array.isArray(a.value)&&e.matchesRatioExactly(a.value)){const e=[];for(let o=0;o<a.value.length;o++){const t=a.value[o];r.isTokenNode(t)&&n.isTokenNumber(t.value)?e.push(t):r.isFunctionNode(t)&&"calc"===t.getName().toLowerCase()&&e.push(t)}if(2!==e.length)return;const i=e[0],u=a.value.indexOf(i),s=e[1],l=a.value.indexOf(s);if(r.isTokenNode(s)&&n.isTokenNumber(s.value)&&0===s.value[4].value)return a.value.splice(u,1,new r.TokenNode([n.TokenType.Number,t.toString(),-1,-1,{value:t,type:n.NumberType.Integer}])),void a.value.splice(l,1,new r.TokenNode([n.TokenType.Number,"1",-1,-1,{value:1,type:n.NumberType.Integer}]));if(r.isTokenNode(i)&&n.isTokenNumber(i.value)&&i.value[4].type===n.NumberType.Integer&&r.isTokenNode(s)&&n.isTokenNumber(s.value)&&s.value[4].type===n.NumberType.Integer)return;let c=null,d=null;if(r.isFunctionNode(i)&&"calc"===i.getName().toLowerCase()){if(i.toString().includes(o.toString()))return;c=modifyCalc(i)}if(r.isFunctionNode(s)&&"calc"===s.getName().toLowerCase()){if(s.toString().includes(o.toString()))return;d=modifyCalc(s)}if(r.isTokenNode(i)&&n.isTokenNumber(i.value)&&r.isTokenNode(s)&&n.isTokenNumber(s.value)){const e=i.value,t=s.value,a=Math.round(e[4].value*o),u=Math.round(t[4].value*o),l=greatestCommonDivisor(a,u);c=new r.TokenNode([n.TokenType.Number,Math.round(a/l).toString(),-1,-1,{value:Math.round(a/l),type:n.NumberType.Integer}]),d=new r.TokenNode([n.TokenType.Number,Math.round(u/l).toString(),-1,-1,{value:Math.round(u/l),type:n.NumberType.Integer}])}else{if(r.isTokenNode(i)&&n.isTokenNumber(i.value)){const e=i.value;c=new r.TokenNode([n.TokenType.Number,Math.round(e[4].value*o).toString(),-1,-1,{value:Math.round(e[4].value*o),type:n.NumberType.Integer}])}if(r.isTokenNode(s)&&n.isTokenNumber(s.value)){const e=s.value;d=new r.TokenNode([n.TokenType.Number,Math.round(e[4].value*o).toString(),-1,-1,{value:Math.round(e[4].value*o),type:n.NumberType.Integer}])}}return c&&d?(a.value.splice(u,1,c),void a.value.splice(l,1,d)):void 0}const i=Array.isArray(a.value)?a.value:[a.value];for(let e=0;e<i.length;e++){const t=i[e];if(r.isTokenNode(t)){const u=t.value;if(!n.isTokenNumber(u))return;if(u[4].type===n.NumberType.Integer)return i.splice(e+1,0,new r.TokenNode([n.TokenType.Delim,"/",-1,-1,{value:"/"}]),new r.TokenNode([n.TokenType.Number,"1",-1,-1,{value:1,type:n.NumberType.Integer}])),void(a.value=i);if(u[4].type===n.NumberType.Number){const t=Math.round(u[4].value*o),s=greatestCommonDivisor(t,o);return i.splice(e,1,new r.TokenNode([n.TokenType.Number,Math.round(t/s).toString(),-1,-1,{value:Math.round(t/s),type:n.NumberType.Integer}]),new r.TokenNode([n.TokenType.Delim,"/",-1,-1,{value:"/"}]),new r.TokenNode([n.TokenType.Number,Math.round(o/s).toString(),-1,-1,{value:Math.round(o/s),type:n.NumberType.Integer}])),void(a.value=i)}return}if(r.isFunctionNode(t)&&"calc"===t.getName().toLowerCase())return i.splice(e,1,modifyCalc(t),new r.TokenNode([n.TokenType.Delim,"/",-1,-1,{value:"/"}]),new r.TokenNode([n.TokenType.Number,o.toString(),-1,-1,{value:o,type:n.NumberType.Integer}])),void(a.value=i)}}function modifyCalc(e){return new r.FunctionNode([n.TokenType.Function,"calc(",-1,-1,{value:"calc("}],[n.TokenType.CloseParen,")",-1,-1,void 0],[new r.SimpleBlockNode([n.TokenType.OpenParen,"(",-1,-1,void 0],[n.TokenType.CloseParen,")",-1,-1,void 0],e.value),new r.WhitespaceNode([[n.TokenType.Whitespace," ",-1,-1,void 0]]),new r.TokenNode([n.TokenType.Delim,"*",-1,-1,{value:"*"}]),new r.WhitespaceNode([[n.TokenType.Whitespace," ",-1,-1,void 0]]),new r.TokenNode([n.TokenType.Number,o.toString(),-1,-1,{value:o,type:n.NumberType.Integer}])])}function greatestCommonDivisor(e,r){if(Number.isNaN(e)||Number.isNaN(r))throw new Error("Unexpected 'NaN' when calculating the greatest common divisor.");if(!Number.isFinite(e)||!Number.isFinite(r))throw new Error("Unexpected 'Infinite' value when calculating the greatest common divisor.");for(r>e&&([e,r]=[r,e]);;){if(0==r)return e;if(0==(e%=r))return r;r%=e}}const a=new Set(["aspect-ratio","min-aspect-ratio","max-aspect-ratio","device-aspect-ratio","min-device-aspect-ratio","max-device-aspect-ratio"]);function transformMediaQueryList(r,n){const o=e.parse(r,{preserveInvalidMediaQueries:!0,onParseError:()=>{throw new Error(`Unable to parse media query "${r}"`)}}),t=new Set(o.map((e=>e.toString())));return o.flatMap((r=>{if(e.isMediaQueryInvalid(r))return[r.toString()];const o=e.cloneMediaQuery(r);o.walk((r=>{const n=r.node;if(e.isMediaFeaturePlain(n)||e.isMediaFeatureRangeNameValue(n)||e.isMediaFeatureRangeValueName(n)){const e=n.name.getName().toLowerCase();if(!a.has(e))return;transformMediaFeatureValue(n.value)}else if(e.isMediaFeatureRangeValueNameValue(n)){const e=n.name.getName().toLowerCase();if(!a.has(e))return;transformMediaFeatureValue(n.valueOne);transformMediaFeatureValue(n.valueTwo)}else;}));const i=r.toString(),u=o.toString();return u===i||t.has(u)?[i]:n?[i,u]:[u]})).join(",")}const creator=e=>{const r=Object.assign({preserve:!1},e);return{postcssPlugin:"postcss-media-queries-aspect-ratio-number-values",AtRule(e,{result:n}){if("media"!==e.name.toLowerCase())return;const o=e.params.toLowerCase();if(!(o.includes("aspect-ratio")||o.includes("min-aspect-ratio")||o.includes("max-aspect-ratio")||o.includes("device-aspect-ratio")||o.includes("min-device-aspect-ratio")||o.includes("max-device-aspect-ratio")))return;let t;try{if(t=transformMediaQueryList(e.params,r.preserve),t===e.params)return}catch(r){return void e.warn(n,`Failed to transform @media params for "${e.params}" with message: "${r instanceof Error?r.message:r}"`)}e.cloneBefore({params:t}),e.remove()}}};creator.postcss=!0,module.exports=creator;
