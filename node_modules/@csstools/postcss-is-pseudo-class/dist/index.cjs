"use strict";var e=require("postcss-selector-parser"),s=require("@csstools/selector-specificity");function alwaysValidSelector(s){const o=e().astSync(s);let n=!0;return o.walk((e=>{if("class"!==e.type&&"comment"!==e.type&&"id"!==e.type&&"root"!==e.type&&"selector"!==e.type&&"string"!==e.type&&"tag"!==e.type&&"universal"!==e.type&&("attribute"!==e.type||e.insensitive)&&("combinator"!==e.type||"+"!==e.value&&">"!==e.value&&"~"!==e.value&&" "!==e.value)&&("pseudo"!==e.type||e.nodes?.length||":hover"!==e.value.toLowerCase()&&":focus"!==e.value.toLowerCase())){if("pseudo"===e.type&&1===e.nodes?.length&&":not"===e.value.toLowerCase()){let s=!0;if(e.nodes[0].walkCombinators((()=>{s=!1})),s)return}return n=!1,!1}})),n}function sortCompoundSelectorsInsideComplexSelector(s){if(!s||!s.nodes||1===s.nodes.length)return;const o=[];let n=[];for(let t=0;t<s.nodes.length;t++)"combinator"!==s.nodes[t].type?e.isPseudoElement(s.nodes[t])?(o.push(n),n=[s.nodes[t]]):n.push(s.nodes[t]):(o.push(n),o.push([s.nodes[t]]),n=[]);o.push(n);const t=[];for(let e=0;e<o.length;e++){const s=o[e];s.sort(((e,s)=>"selector"===e.type&&"selector"===s.type&&e.nodes.length&&s.nodes.length?selectorTypeOrder(e.nodes[0],e.nodes[0].type)-selectorTypeOrder(s.nodes[0],s.nodes[0].type):"selector"===e.type&&e.nodes.length?selectorTypeOrder(e.nodes[0],e.nodes[0].type)-selectorTypeOrder(s,s.type):"selector"===s.type&&s.nodes.length?selectorTypeOrder(e,e.type)-selectorTypeOrder(s.nodes[0],s.nodes[0].type):selectorTypeOrder(e,e.type)-selectorTypeOrder(s,s.type)));const n=new Set(s.map((e=>e.type))),r=n.has("universal")&&(n.has("tag")||n.has("attribute")||n.has("class")||n.has("id")||n.has("pseudo"));for(let e=0;e<s.length;e++)"universal"===s[e].type&&r?s[e].remove():t.push(s[e])}s.removeAll();for(let o=t.length-1;o>=0;o--){const n=t[o-1];if(t[o].remove(),n&&"tag"===n.type&&"tag"===t[o].type){const n=e.pseudo({value:":is",nodes:[e.selector({value:"",nodes:[t[o]]})]});n.parent=s,s.nodes.unshift(n)}else t[o].parent=s,s.nodes.unshift(t[o])}}function selectorTypeOrder(s,n){return e.isPseudoElement(s)?o.pseudoElement:o[n]}const o={universal:0,tag:1,pseudoElement:2,id:3,class:4,attribute:5,pseudo:6,selector:7,string:8,root:9,comment:10};function childAdjacentChild(e){return!(!e||!e.nodes)&&("selector"===e.type&&(3===e.nodes.length&&(!(!e.nodes[0]||"pseudo"!==e.nodes[0].type||":-csstools-matches"!==e.nodes[0].value)&&(!(!e.nodes[1]||"combinator"!==e.nodes[1].type||"+"!==e.nodes[1].value&&"~"!==e.nodes[1].value)&&(!(!e.nodes[2]||"pseudo"!==e.nodes[2].type||":-csstools-matches"!==e.nodes[2].value)&&(!(!e.nodes[0].nodes||1!==e.nodes[0].nodes.length)&&("selector"===e.nodes[0].nodes[0].type&&(!(!e.nodes[0].nodes[0].nodes||3!==e.nodes[0].nodes[0].nodes.length)&&(!(!e.nodes[0].nodes[0].nodes||"combinator"!==e.nodes[0].nodes[0].nodes[1].type||">"!==e.nodes[0].nodes[0].nodes[1].value)&&(!(!e.nodes[2].nodes||1!==e.nodes[2].nodes.length)&&("selector"===e.nodes[2].nodes[0].type&&(!(!e.nodes[2].nodes[0].nodes||3!==e.nodes[2].nodes[0].nodes.length)&&(!(!e.nodes[2].nodes[0].nodes||"combinator"!==e.nodes[2].nodes[0].nodes[1].type||">"!==e.nodes[2].nodes[0].nodes[1].value)&&(e.nodes[0].nodes[0].insertAfter(e.nodes[0].nodes[0].nodes[0],e.nodes[2].nodes[0].nodes[0].clone()),e.nodes[2].nodes[0].nodes[1].remove(),e.nodes[2].nodes[0].nodes[0].remove(),e.nodes[0].replaceWith(e.nodes[0].nodes[0]),e.nodes[2].replaceWith(e.nodes[2].nodes[0]),!0))))))))))))))}function isInCompoundWithOneOtherElement(s){if(!s||!s.nodes)return!1;if(!e.isSelector(s))return!1;if(2!==s.nodes.length)return!1;let o=-1,n=-1;s.nodes[0]&&e.isPseudoClass(s.nodes[0])&&":-csstools-matches"===s.nodes[0].value?(o=0,n=1):s.nodes[1]&&e.isPseudoClass(s.nodes[1])&&":-csstools-matches"===s.nodes[1].value&&(o=1,n=0);const t=s.nodes[o];if(!t||!e.isPseudoClass(t)||1!==t.nodes.length)return!1;const r=s.nodes[n];return!!r&&(!e.isCombinator(r)&&(!e.isPseudoElement(r)&&(t.nodes[0].append(r.clone()),t.replaceWith(...t.nodes[0].nodes),r.remove(),!0)))}function isPseudoInFirstCompound(s){if(!s||!s.nodes)return!1;if(!e.isSelector(s))return!1;let o=-1;for(let n=0;n<s.nodes.length;n++){const t=s.nodes[n];if(e.isCombinator(t))return!1;if(e.isPseudoElement(t))return!1;if(e.isPseudoClass(t)){const e=t;if(":-csstools-matches"===e.value){if(!e.nodes||1!==e.nodes.length)return!1;o=n;break}}}if(-1===o)return!1;const n=s.nodes[o];if(!n||!e.isPseudoClass(n))return!1;const t=s.nodes.slice(0,o),r=s.nodes.slice(o+1);return t.forEach((e=>{n.nodes[0].append(e.clone())})),r.forEach((e=>{n.nodes[0].append(e.clone())})),n.replaceWith(...n.nodes),t.forEach((e=>{e.remove()})),r.forEach((e=>{e.remove()})),!0}function samePrecedingElement(s){if(!s||!s.nodes)return!1;if("selector"!==s.type)return!1;let o=-1;for(let n=0;n<s.nodes.length;n++){const t=s.nodes[n];if(e.isCombinator(t)){o=n;break}if(e.isPseudoElement(t))return!1}if(-1===o)return!1;const n=o+1;if(!s.nodes[o]||"combinator"!==s.nodes[o].type||">"!==s.nodes[o].value&&"+"!==s.nodes[o].value)return!1;const t=s.nodes[o].value;if(!s.nodes[n]||"pseudo"!==s.nodes[n].type||":-csstools-matches"!==s.nodes[n].value)return!1;if(!s.nodes[n].nodes||1!==s.nodes[n].nodes.length)return!1;if("selector"!==s.nodes[n].nodes[0].type)return!1;if(!s.nodes[n].nodes[0].nodes||3!==s.nodes[n].nodes[0].nodes.length)return!1;if(!s.nodes[n].nodes[0].nodes||"combinator"!==s.nodes[n].nodes[0].nodes[1].type||s.nodes[n].nodes[0].nodes[1].value!==t)return!1;const r=s.nodes[n];if(!r||!e.isPseudoClass(r))return!1;const d=s.nodes.slice(0,o),l=s.nodes.slice(n+1);return s.each((e=>{e.remove()})),d.forEach((e=>{s.append(e)})),r.nodes[0].nodes.forEach((e=>{s.append(e)})),l.forEach((e=>{s.append(e)})),!0}function complexSelectors(s,o,n,t){return s.flatMap((s=>{if(-1===s.indexOf(":-csstools-matches")&&-1===s.toLowerCase().indexOf(":is"))return s;const r=e().astSync(s);return r.walkPseudos((s=>{if(":is"===s.value.toLowerCase()&&s.nodes&&s.nodes.length&&"selector"===s.nodes[0].type&&0===s.nodes[0].nodes.length)return s.value=":not",void s.nodes[0].append(e.universal());if(":-csstools-matches"===s.value)if(!s.nodes||s.nodes.length){if(s.walkPseudos((s=>{if(e.isPseudoElement(s)){let e=s.value;if(e.startsWith("::-csstools-invalid-"))return;for(;e.startsWith(":");)e=e.slice(1);s.value=`::-csstools-invalid-${e}`,t()}})),1===s.nodes.length&&"selector"===s.nodes[0].type){if(1===s.nodes[0].nodes.length)return void s.replaceWith(s.nodes[0].nodes[0]);if(!s.nodes[0].some((e=>"combinator"===e.type)))return void s.replaceWith(...s.nodes[0].nodes)}1!==r.nodes.length||"selector"!==r.nodes[0].type||1!==r.nodes[0].nodes.length||r.nodes[0].nodes[0]!==s?childAdjacentChild(s.parent)||isInCompoundWithOneOtherElement(s.parent)||isPseudoInFirstCompound(s.parent)||samePrecedingElement(s.parent)||("warning"===o.onComplexSelector&&n(),s.value=":is"):s.replaceWith(...s.nodes[0].nodes)}else s.remove()})),r.walk((e=>{"selector"===e.type&&"nodes"in e&&1===e.nodes.length&&"selector"===e.nodes[0].type&&e.replaceWith(e.nodes[0])})),r.walk((e=>{"nodes"in e&&sortCompoundSelectorsInsideComplexSelector(e)})),r.toString()})).filter((e=>!!e))}function splitSelectors(o,n,t=0){const r=":not(#"+n.specificityMatchingName+")",d=":not(."+n.specificityMatchingName+")",l=":not("+n.specificityMatchingName+")";return o.flatMap((o=>{if(-1===o.toLowerCase().indexOf(":is"))return o;let i=!1;const a=[];if(e().astSync(o).walkPseudos((e=>{if(":is"!==e.value.toLowerCase()||!e.nodes||!e.nodes.length)return;if("selector"===e.nodes[0].type&&0===e.nodes[0].nodes.length)return;if("pseudo"===e.parent?.parent?.type&&":not"===e.parent?.parent?.value?.toLowerCase())return void a.push([{start:e.parent.parent.sourceIndex,end:e.parent.parent.sourceIndex+e.parent.parent.toString().length,option:`:not(${e.nodes.toString()})`}]);if("pseudo"===e.parent?.parent?.type&&":has"===e.parent?.parent?.value?.toLowerCase())return void(e.value=":-csstools-matches");let o=e.parent;for(;o;){if(o.value&&":is"===o.value.toLowerCase()&&"pseudo"===o.type)return void(i=!0);o=o.parent}const n=s.selectorSpecificity(e),t=e.sourceIndex,c=t+e.toString().length,u=[];e.nodes.forEach((e=>{const o={start:t,end:c,option:""},i=s.selectorSpecificity(e);let a=e.toString().trim();const p=Math.max(0,n.a-i.a),h=Math.max(0,n.b-i.b),f=Math.max(0,n.c-i.c);for(let e=0;e<p;e++)a+=r;for(let e=0;e<h;e++)a+=d;for(let e=0;e<f;e++)a+=l;o.option=a,u.push(o)})),a.push(u)})),!a.length)return[o];let c=[];return cartesianProduct(...a).forEach((e=>{let s="";for(let n=0;n<e.length;n++){const t=e[n];s+=o.substring(e[n-1]?.end||0,e[n].start),s+=":-csstools-matches("+t.option+")",n===e.length-1&&(s+=o.substring(e[n].end))}c.push(s)})),i&&t<10&&(c=splitSelectors(c,n,t+1)),c})).filter((e=>!!e))}function cartesianProduct(...e){const s=[],o=e.length-1;return function helper(n,t){for(let r=0,d=e[t].length;r<d;r++){const d=n.slice(0);d.push(e[t][r]),t===o?s.push(d):helper(d,t+1)}}([],0),s}const n=/:is\(/i,creator=e=>{const s={specificityMatchingName:"does-not-exist",...e||{}};return{postcssPlugin:"postcss-is-pseudo-class",prepare(){const e=new WeakSet;return{postcssPlugin:"postcss-is-pseudo-class",Rule(o,{result:t}){if(!o.selector)return;if(!n.test(o.selector))return;if(e.has(o))return;let r=!1;const warnOnComplexSelector=()=>{"warning"===s.onComplexSelector&&(r||(r=!0,o.warn(t,`Complex selectors in '${o.selector}' can not be transformed to an equivalent selector without ':is()'.`)))};let d=!1;const warnOnPseudoElements=()=>{"warning"===s.onPseudoElement&&(d||(d=!0,o.warn(t,`Pseudo elements are not allowed in ':is()', unable to transform '${o.selector}'`)))};try{let n=!1;const t=[],r=complexSelectors(splitSelectors(o.selectors,{specificityMatchingName:s.specificityMatchingName}),{onComplexSelector:s.onComplexSelector},warnOnComplexSelector,warnOnPseudoElements);if(Array.from(new Set(r)).forEach((s=>{if(o.selectors.indexOf(s)>-1)t.push(s);else{if(alwaysValidSelector(s))return t.push(s),void(n=!0);e.add(o),o.cloneBefore({selector:s}),n=!0}})),t.length&&n&&(e.add(o),o.cloneBefore({selectors:t})),!s.preserve){if(!n)return;o.remove()}}catch(e){if(!(e instanceof Error))throw e;if(e.message.indexOf("call stack size exceeded")>-1)throw e;o.warn(t,`Failed to parse selector "${o.selector}" with error: ${e.message}`)}}}}}};creator.postcss=!0,module.exports=creator;
