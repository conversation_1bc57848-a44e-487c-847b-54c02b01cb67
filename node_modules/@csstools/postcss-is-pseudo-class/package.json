{"name": "@csstools/postcss-is-pseudo-class", "description": "A pseudo-class for matching elements in a selector list", "version": "5.0.3", "author": "<PERSON> <<EMAIL>>", "license": "MIT-0", "funding": [{"type": "github", "url": "https://github.com/sponsors/csstools"}, {"type": "opencollective", "url": "https://opencollective.com/csstools"}], "engines": {"node": ">=18"}, "type": "module", "main": "dist/index.cjs", "module": "dist/index.mjs", "exports": {".": {"import": {"types": "./dist/index.d.ts", "default": "./dist/index.mjs"}, "require": {"default": "./dist/index.cjs"}}}, "files": ["CHANGELOG.md", "LICENSE.md", "README.md", "dist"], "dependencies": {"@csstools/selector-specificity": "^5.0.0", "postcss-selector-parser": "^7.0.0"}, "peerDependencies": {"postcss": "^8.4"}, "scripts": {}, "homepage": "https://github.com/csstools/postcss-plugins/tree/main/plugins/postcss-is-pseudo-class#readme", "repository": {"type": "git", "url": "git+https://github.com/csstools/postcss-plugins.git", "directory": "plugins/postcss-is-pseudo-class"}, "bugs": "https://github.com/csstools/postcss-plugins/issues", "keywords": ["css", "is", "matches", "polyfill", "postcss", "postcss-plugin", "pseudo", "selector"]}