# Utilities

[<img alt="npm version" src="https://img.shields.io/npm/v/@csstools/utilities.svg" height="20">][npm-url]
[<img alt="Build Status" src="https://github.com/csstools/postcss-plugins/workflows/test/badge.svg" height="20">][cli-url]
[<img alt="Discord" src="https://shields.io/badge/Discord-5865F2?logo=discord&logoColor=white">][discord]

Helpers that we use in many PostCSS plugins.

## API

[Read the API docs](./docs/utilities.md)

## Usage

Add [Utilities] to your project:

```bash
npm install @csstools/utilities --save-dev
```

[cli-url]: https://github.com/csstools/postcss-plugins/actions/workflows/test.yml?query=workflow/test
[discord]: https://discord.gg/bUadyRwkJS
[npm-url]: https://www.npmjs.com/package/@csstools/utilities

[Utilities]: https://github.com/csstools/postcss-plugins/tree/main/packages/utilities
