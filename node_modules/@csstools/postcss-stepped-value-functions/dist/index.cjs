"use strict";var e=require("@csstools/css-calc");const s=/(?<![-\w])(?:mod|rem|round)\(/i,creator=o=>{const t=Object.assign({preserve:!1},o);return{postcssPlugin:"postcss-stepped-value-functions",Declaration(o){if(!s.test(o.value))return;const c=e.calc(o.value,{precision:5,toCanonicalUnits:!0});c!==o.value&&(o.cloneBefore({value:c}),t.preserve||o.remove())}}};creator.postcss=!0,module.exports=creator;
