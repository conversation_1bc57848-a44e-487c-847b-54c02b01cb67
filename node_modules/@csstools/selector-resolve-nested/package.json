{"name": "@csstools/selector-resolve-nested", "description": "Resolve nested CSS selectors", "version": "3.1.0", "contributors": [{"name": "Antonio <PERSON>", "email": "<EMAIL>", "url": "https://antonio.laguna.es"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "license": "MIT-0", "funding": [{"type": "github", "url": "https://github.com/sponsors/csstools"}, {"type": "opencollective", "url": "https://opencollective.com/csstools"}], "engines": {"node": ">=18"}, "type": "module", "main": "dist/index.cjs", "module": "dist/index.mjs", "exports": {".": {"import": {"types": "./dist/index.d.ts", "default": "./dist/index.mjs"}, "require": {"default": "./dist/index.cjs"}}}, "files": ["CHANGELOG.md", "LICENSE.md", "README.md", "dist"], "peerDependencies": {"postcss-selector-parser": "^7.0.0"}, "scripts": {}, "homepage": "https://github.com/csstools/postcss-plugins/tree/main/packages/selector-resolve-nested#readme", "repository": {"type": "git", "url": "git+https://github.com/csstools/postcss-plugins.git", "directory": "packages/selector-resolve-nested"}, "bugs": "https://github.com/csstools/postcss-plugins/issues", "keywords": ["css", "nested", "postcss-selector-parser"]}