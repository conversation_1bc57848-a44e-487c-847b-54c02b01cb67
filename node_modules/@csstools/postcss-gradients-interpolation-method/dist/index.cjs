"use strict";var e=require("@csstools/postcss-progressive-custom-properties"),o=require("@csstools/utilities"),t=require("@csstools/css-parser-algorithms"),i=require("@csstools/css-tokenizer"),n=require("@csstools/css-color-parser");const s=/(?:repeating-)?(?:linear|radial|conic)-gradient\(/i,r=/\bin\b/i,a={test:e=>s.test(e)&&r.test(e)},l=/^(repeating-)?(linear|radial|conic)-gradient$/i;function interpolateColorsInColorStopsList(e,o,s,r=!1){const a=[],l=[];for(let r=0;r<e.length-1;r++){const a=e[r],c=e[r+1];if(l.push(a),s||n.serializeP3(a.colorData,!1).toString()!==n.serializeP3(c.colorData,!1).toString()&&a.position.toString()!==c.position.toString())for(let e=1;e<=9;e++){const r=10*e;let u=[];s&&(u=[new t.WhitespaceNode([[i.TokenType.Whitespace," ",-1,-1,void 0]]),s,new t.WhitespaceNode([[i.TokenType.Whitespace," ",-1,-1,void 0]]),new t.TokenNode([i.TokenType.Ident,"hue",-1,-1,{value:"hue"}])]);const p=new t.FunctionNode([i.TokenType.Function,"color-mix(",-1,-1,{value:"color-mix"}],[i.TokenType.CloseParen,")",-1,-1,void 0],[new t.TokenNode([i.TokenType.Ident,"in",-1,-1,{value:"in"}]),new t.WhitespaceNode([[i.TokenType.Whitespace," ",-1,-1,void 0]]),o,...u,new t.TokenNode([i.TokenType.Comma,",",-1,-1,void 0]),new t.WhitespaceNode([[i.TokenType.Whitespace," ",-1,-1,void 0]]),a.color,new t.WhitespaceNode([[i.TokenType.Whitespace," ",-1,-1,void 0]]),new t.TokenNode([i.TokenType.Percentage,100-r+"%",-1,-1,{value:100-r}]),new t.TokenNode([i.TokenType.Comma,",",-1,-1,void 0]),new t.WhitespaceNode([[i.TokenType.Whitespace," ",-1,-1,void 0]]),c.color,new t.WhitespaceNode([[i.TokenType.Whitespace," ",-1,-1,void 0]]),new t.TokenNode([i.TokenType.Percentage,`${r}%`,-1,-1,{value:r}])]),d=n.color(p);if(!d)return!1;l.push({colorData:d})}r===e.length-2&&l.push(c)}for(let e=0;e<l.length;e++)r&&!n.colorDataFitsRGB_Gamut(l[e].colorData)?l[e].color=n.serializeP3(l[e].colorData,!1):l[e].color=n.serializeRGB(l[e].colorData,!1);for(let e=0;e<l.length;e++){const o=l[e];o.position?a.push(o.color,new t.WhitespaceNode([[i.TokenType.Whitespace," ",-1,-1,void 0]]),o.position):a.push(o.color),e!==l.length-1&&a.push(new t.TokenNode([i.TokenType.Comma,",",-1,-1,void 0]),new t.WhitespaceNode([[i.TokenType.Whitespace," ",-1,-1,void 0]]))}return a}function parseColorStops(e){const o=[];let s={};for(let r=0;r<e.length;r++){const a=e[r];if(t.isCommentNode(a)||t.isWhitespaceNode(a))continue;if(t.isTokenNode(a)&&i.isTokenComma(a.value)){if(s.color&&s.colorData&&s.positionA){o.push({color:s.color,colorData:s.colorData,position:s.positionA}),s.positionB&&o.push({color:s.color,colorData:s.colorData,position:s.positionB}),s={};continue}return!1}const l=n.color(a);if(l){if(s.color)return!1;if(l.syntaxFlags.has(n.SyntaxFlag.Experimental))return!1;s.color=a,s.colorData=l}else{if(!s.color)return!1;if(s.positionA){if(!s.positionA||s.positionB)return!1;s.positionB=a}else s.positionA=a}}return!(!s.color||!s.positionA)&&(s.color&&s.colorData&&s.positionA&&(o.push({color:s.color,colorData:s.colorData,position:s.positionA}),s.positionB&&o.push({color:s.color,colorData:s.colorData,position:s.positionB})),!(o.length<2)&&o)}const c=/^(?:srgb|srgb-linear|lab|oklab|xyz|xyz-d50|xyz-d65|hsl|hwb|lch|oklch)$/i,u=/^(?:hsl|hwb|lch|oklch)$/i,p=/^(?:shorter|longer|increasing|decreasing)$/i,d=/^in$/i,v=/^hue$/i;function modifyGradientFunctionComponentValues(e,o=!1){const n=e.getName();if(!l.test(n))return!1;let s="srgb",r=null,a=null,h=null,T=null,f=null,m=[];{let o=0,n=e.value[o];for(;n&&!(t.isTokenNode(n)&&i.isTokenIdent(n.value)&&d.test(n.value[4].value));){if(t.isTokenNode(n)&&i.isTokenComma(n.value))return!1;o++,n=e.value[o]}for(r=n,o++,n=e.value[o];t.isCommentNode(n)||t.isWhitespaceNode(n);)o++,n=e.value[o];if(t.isTokenNode(n)&&i.isTokenIdent(n.value)&&c.test(n.value[4].value)){if(a)return!1;a=n,s=n.value[4].value,o++,n=e.value[o]}for(;t.isCommentNode(n)||t.isWhitespaceNode(n);)o++,n=e.value[o];if(t.isTokenNode(n)&&i.isTokenIdent(n.value)&&p.test(n.value[4].value)&&u.test(s)){if(h||!a)return!1;h=n,o++,n=e.value[o]}for(;t.isCommentNode(n)||t.isWhitespaceNode(n);)o++,n=e.value[o];if(t.isTokenNode(n)&&i.isTokenIdent(n.value)&&v.test(n.value[4].value)){if(T||!a||!h)return!1;T=n,o++,n=e.value[o]}for(;n&&(!t.isTokenNode(n)||!i.isTokenComma(n.value));)o++,n=e.value[o];if(f=n,!f)return!1;m=e.value.slice(o+1)}if(!a)return!1;if(h&&!T)return!1;if(T&&!h)return!1;const k=parseColorStops(m);if(!k)return!1;const g=interpolateColorsInColorStopsList(k,a,h,o);if(!g)return!1;const N=trim([...e.value.slice(0,e.value.indexOf(r)),...e.value.slice(e.value.indexOf(T||a)+1,e.value.indexOf(f))]);return N.length>0&&N.some((e=>!t.isCommentNode(e)))&&N.push(new t.TokenNode([i.TokenType.Comma,",",-1,-1,void 0]),new t.WhitespaceNode([[i.TokenType.Whitespace," ",-1,-1,void 0]])),trim([...N,...trim(g)])}function trim(e){let o=0,i=e.length-1;for(let i=0;i<e.length;i++)if(!t.isWhitespaceNode(e[i])){o=i;break}for(let o=e.length-1;o>=0;o--)if(!t.isWhitespaceNode(e[o])){i=o;break}return e.slice(o,i+1)}const basePlugin=e=>({postcssPlugin:"postcss-gradients-interpolation-method",Declaration(n){if(!a.test(n.value))return;if(o.hasFallback(n))return;if(o.hasSupportsAtRuleAncestor(n,a))return;const s=i.tokenize({css:n.value}),r=t.stringify(t.replaceComponentValues(t.parseCommaSeparatedListOfComponentValues(s),(e=>{if(!t.isFunctionNode(e))return;const o=modifyGradientFunctionComponentValues(e);o&&(e.value=o)})));if(r===n.value)return;const l=t.stringify(t.replaceComponentValues(t.parseCommaSeparatedListOfComponentValues(s),(e=>{if(!t.isFunctionNode(e))return;const o=modifyGradientFunctionComponentValues(e,!0);o&&(e.value=o)})));n.cloneBefore({value:r}),r!==l&&n.cloneBefore({value:l}),e?.preserve||n.remove()}});basePlugin.postcss=!0;const postcssPlugin=o=>{const t=Object.assign({enableProgressiveCustomProperties:!0,preserve:!0},o);return t.enableProgressiveCustomProperties?{postcssPlugin:"postcss-gradients-interpolation-method",plugins:[e(),basePlugin(t)]}:basePlugin(t)};postcssPlugin.postcss=!0,module.exports=postcssPlugin;
