{"version": 3, "sources": ["../src/createFetchRequester.ts"], "sourcesContent": ["import type { Response as AlgoliaResponse, EndRequest, Requester } from '@algolia/client-common';\n\nfunction isAbortError(error: unknown): boolean {\n  return error instanceof Error && error.name === 'AbortError';\n}\n\nfunction getErrorMessage(error: unknown, abortContent: string): string {\n  if (isAbortError(error)) {\n    return abortContent;\n  }\n  return error instanceof Error ? error.message : 'Network request failed';\n}\n\nexport type FetchRequesterOptions = {\n  readonly requesterOptions?: RequestInit | undefined;\n};\n\nexport function createFetchRequester({ requesterOptions = {} }: FetchRequesterOptions = {}): Requester {\n  async function send(request: EndRequest): Promise<AlgoliaResponse> {\n    const abortController = new AbortController();\n    const signal = abortController.signal;\n\n    const createTimeout = (timeout: number): NodeJS.Timeout => {\n      return setTimeout(() => {\n        abortController.abort();\n      }, timeout);\n    };\n\n    const connectTimeout = createTimeout(request.connectTimeout);\n\n    let fetchRes: Response;\n    try {\n      fetchRes = await fetch(request.url, {\n        method: request.method,\n        body: request.data || null,\n        redirect: 'manual',\n        signal,\n        ...requesterOptions,\n        headers: {\n          ...requesterOptions.headers,\n          ...request.headers,\n        },\n      });\n    } catch (error) {\n      return {\n        status: 0,\n        content: getErrorMessage(error, 'Connection timeout'),\n        isTimedOut: isAbortError(error),\n      };\n    }\n\n    clearTimeout(connectTimeout);\n\n    createTimeout(request.responseTimeout);\n\n    try {\n      const content = await fetchRes.text();\n\n      return {\n        content,\n        isTimedOut: false,\n        status: fetchRes.status,\n      };\n    } catch (error) {\n      return {\n        status: 0,\n        content: getErrorMessage(error, 'Socket timeout'),\n        isTimedOut: isAbortError(error),\n      };\n    }\n  }\n\n  return { send };\n}\n"], "mappings": "AAEA,SAASA,EAAaC,EAAyB,CAC7C,OAAOA,aAAiB,OAASA,EAAM,OAAS,YAClD,CAEA,SAASC,EAAgBD,EAAgBE,EAA8B,CACrE,OAAIH,EAAaC,CAAK,EACbE,EAEFF,aAAiB,MAAQA,EAAM,QAAU,wBAClD,CAMO,SAASG,EAAqB,CAAE,iBAAAC,EAAmB,CAAC,CAAE,EAA2B,CAAC,EAAc,CACrG,eAAeC,EAAKC,EAA+C,CACjE,IAAMC,EAAkB,IAAI,gBACtBC,EAASD,EAAgB,OAEzBE,EAAiBC,GACd,WAAW,IAAM,CACtBH,EAAgB,MAAM,CACxB,EAAGG,CAAO,EAGNC,EAAiBF,EAAcH,EAAQ,cAAc,EAEvDM,EACJ,GAAI,CACFA,EAAW,MAAM,MAAMN,EAAQ,IAAK,CAClC,OAAQA,EAAQ,OAChB,KAAMA,EAAQ,MAAQ,KACtB,SAAU,SACV,OAAAE,EACA,GAAGJ,EACH,QAAS,CACP,GAAGA,EAAiB,QACpB,GAAGE,EAAQ,OACb,CACF,CAAC,CACH,OAASN,EAAO,CACd,MAAO,CACL,OAAQ,EACR,QAASC,EAAgBD,EAAO,oBAAoB,EACpD,WAAYD,EAAaC,CAAK,CAChC,CACF,CAEA,aAAaW,CAAc,EAE3BF,EAAcH,EAAQ,eAAe,EAErC,GAAI,CAGF,MAAO,CACL,QAHc,MAAMM,EAAS,KAAK,EAIlC,WAAY,GACZ,OAAQA,EAAS,MACnB,CACF,OAASZ,EAAO,CACd,MAAO,CACL,OAAQ,EACR,QAASC,EAAgBD,EAAO,gBAAgB,EAChD,WAAYD,EAAaC,CAAK,CAChC,CACF,CACF,CAEA,MAAO,CAAE,KAAAK,CAAK,CAChB", "names": ["isAbortError", "error", "getErrorMessage", "abort<PERSON><PERSON>nt", "createFetchRequester", "requesterOptions", "send", "request", "abortController", "signal", "createTimeout", "timeout", "connectTimeout", "fetchRes"]}