# @algolia/autocomplete-plugin-algolia-insights

The Algolia Insights plugin automatically sends click and conversion events to the [Algolia Insights API](https://www.algolia.com/doc/rest-api/insights]) whenever a user interacts with the autocomplete.

## Installation

```sh
yarn add @algolia/autocomplete-plugin-algolia-insights
# or
npm install @algolia/autocomplete-plugin-algolia-insights
```

## Documentation

See [**Documentation**](https://www.algolia.com/doc/ui-libraries/autocomplete/api-reference/autocomplete-plugin-algolia-insights).
