{"name": "@algolia/autocomplete-shared", "description": "Shared utils for Autocomplete packages.", "version": "1.17.9", "license": "MIT", "homepage": "https://github.com/algolia/autocomplete", "repository": "algolia/autocomplete", "author": {"name": "Algolia, Inc.", "url": "https://www.algolia.com"}, "source": "src/index.ts", "types": "dist/esm/index.d.ts", "module": "dist/esm/index.js", "main": "dist/esm/index.js", "sideEffects": false, "files": ["dist/"], "scripts": {"build:clean": "rm -rf ./dist", "build:esm": "babel src --root-mode upward --extensions '.ts,.tsx' --out-dir dist/esm --ignore '**/*/__tests__/'", "build:types": "tsc -p ./tsconfig.declaration.json --outDir ./dist/esm", "build": "yarn build:clean && yarn build:esm && yarn build:types", "on:change": "concurrently \"yarn build:esm\" \"yarn build:types\"", "prepare": "yarn build:esm && yarn build:types", "watch": "watch \"yarn on:change\" --ignoreDirectoryPattern \"/dist/\""}, "devDependencies": {"@algolia/client-search": "4.16.0", "algoliasearch": "4.16.0"}, "peerDependencies": {"@algolia/client-search": ">= 4.9.1 < 6", "algoliasearch": ">= 4.9.1 < 6"}}