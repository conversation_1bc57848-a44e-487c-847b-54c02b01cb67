# @algolia/autocomplete-core

The [`autocomplete-core`](https://www.algolia.com/doc/ui-libraries/autocomplete/api-reference/autocomplete-core/createAutocomplete) package is the foundation of Autocomplete. It exposes primitives to build an autocomplete experience.

You likely don’t need to use this package directly unless you’re building a [renderer](https://www.algolia.com/doc/ui-libraries/autocomplete/guides/creating-a-renderer).

## Installation

```sh
yarn add @algolia/autocomplete-core
# or
npm install @algolia/autocomplete-core
```

## Documentation

See [**Documentation**](https://www.algolia.com/doc/ui-libraries/autocomplete/api-reference/autocomplete-core).
