{"name": "@algolia/requester-node-http", "version": "5.29.0", "description": "Promise-based request library for node using the native http module.", "repository": {"type": "git", "url": "git+https://github.com/algolia/algoliasearch-client-javascript.git"}, "homepage": "https://github.com/algolia/algoliasearch-client-javascript#readme", "license": "MIT", "author": "Algolia", "type": "module", "files": ["dist", "index.d.ts", "index.js"], "exports": {".": {"types": {"import": "./dist/requester.http.d.ts", "module": "./dist/requester.http.d.ts", "require": "./dist/requester.http.d.cts"}, "import": "./dist/requester.http.js", "module": "./dist/requester.http.js", "require": "./dist/requester.http.cjs"}, "./src/*": "./src/*.ts"}, "scripts": {"build": "yarn clean && yarn tsup", "clean": "rm -rf ./dist || true", "test": "tsc --noEmit && vitest --run", "test:bundle": "publint . && attw --pack ."}, "dependencies": {"@algolia/client-common": "5.29.0"}, "devDependencies": {"@arethetypeswrong/cli": "0.18.2", "@types/node": "22.15.31", "nock": "14.0.5", "publint": "0.3.12", "tsup": "8.5.0", "typescript": "5.8.3", "vitest": "3.2.3"}, "engines": {"node": ">= 14.0.0"}}