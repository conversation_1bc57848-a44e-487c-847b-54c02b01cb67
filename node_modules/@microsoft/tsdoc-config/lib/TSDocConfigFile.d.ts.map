{"version": 3, "file": "TSDocConfigFile.d.ts", "sourceRoot": "", "sources": ["../src/TSDocConfigFile.ts"], "names": [], "mappings": "AAGA,OAAO,EACL,kBAAkB,EAElB,KAAK,kBAAkB,EACvB,gBAAgB,EAKhB,KAAK,6BAA6B,EACnC,MAAM,kBAAkB,CAAC;AAwC1B;;;;GAIG;AACH,qBAAa,eAAe;IAC1B,gBAAuB,QAAQ,EAAE,MAAM,CAAgB;IACvD,gBAAuB,kBAAkB,EAAE,MAAM,CAC2B;IAE5E;;OAEG;IACH,SAAgB,GAAG,EAAE,gBAAgB,CAAC;IAEtC,OAAO,CAAC,QAAQ,CAAC,aAAa,CAAoB;IAClD,OAAO,CAAC,SAAS,CAAS;IAC1B,OAAO,CAAC,aAAa,CAAU;IAC/B,OAAO,CAAC,UAAU,CAAS;IAC3B,OAAO,CAAC,UAAU,CAAU;IAC5B,OAAO,CAAC,YAAY,CAAS;IAC7B,OAAO,CAAC,QAAQ,CAAC,aAAa,CAAW;IACzC,OAAO,CAAC,eAAe,CAAsB;IAC7C,OAAO,CAAC,QAAQ,CAAC,eAAe,CAAuB;IACvD,OAAO,CAAC,QAAQ,CAAC,mBAAmB,CAAc;IAClD,OAAO,CAAC,QAAQ,CAAC,eAAe,CAAuB;IACvD,OAAO,CAAC,sBAAsB,CAA0B;IACxD,OAAO,CAAC,8BAA8B,CAAsB;IAE5D,OAAO;IAgBP;;OAEG;IACH,IAAW,YAAY,IAAI,aAAa,CAAC,eAAe,CAAC,CAExD;IAED;;;OAGG;IACH,IAAW,QAAQ,IAAI,MAAM,CAE5B;IAED;;;;;;OAMG;IACH,IAAW,YAAY,IAAI,OAAO,CAEjC;IAED;;;;;;;;OAQG;IACH,IAAW,SAAS,IAAI,OAAO,CAE9B;IAED;;OAEG;IACH,IAAW,WAAW,IAAI,MAAM,CAE/B;IAED;;;OAGG;IACH,IAAW,YAAY,IAAI,aAAa,CAAC,MAAM,CAAC,CAE/C;IAED;;;;;;;;;;OAUG;IACH,IAAW,cAAc,IAAI,OAAO,GAAG,SAAS,CAE/C;IAED,IAAW,cAAc,CAAC,KAAK,EAAE,OAAO,GAAG,SAAS,EAEnD;IAED,IAAW,cAAc,IAAI,aAAa,CAAC,kBAAkB,CAAC,CAE7D;IAED,IAAW,cAAc,IAAI,WAAW,CAAC,MAAM,EAAE,OAAO,CAAC,CAExD;IAED,IAAW,qBAAqB,IAAI,aAAa,CAAC,MAAM,CAAC,GAAG,SAAS,CAEpE;IAED,IAAW,6BAA6B,IAAI,OAAO,GAAG,SAAS,CAE9D;IAED,IAAW,6BAA6B,CAAC,KAAK,EAAE,OAAO,GAAG,SAAS,EAElE;IAED;;OAEG;IACI,mBAAmB,IAAI,IAAI;IAKlC;;OAEG;IACI,gBAAgB,CAAC,UAAU,EAAE,6BAA6B,GAAG,IAAI;IAaxE,OAAO,CAAC,wBAAwB;IA0BhC;;OAEG;IACI,uBAAuB,CAAC,WAAW,EAAE,MAAM,GAAG,IAAI;IAOzD;;OAEG;IACI,0BAA0B,IAAI,IAAI;IAIzC;;OAEG;IACI,mBAAmB,IAAI,IAAI;IAIlC;;OAEG;IACI,gBAAgB,CAAC,OAAO,EAAE,MAAM,EAAE,SAAS,EAAE,OAAO,GAAG,IAAI;IAKlE;;;;;;;;;;;;OAYG;IACI,qBAAqB,IAAI,OAAO;IAYvC;;;;OAIG;IACH,OAAO,CAAC,qBAAqB;IAa7B,OAAO,CAAC,YAAY;IAKpB,OAAO,CAAC,eAAe;IAwEvB,OAAO,CAAC,gBAAgB;IA+FxB;;;;;OAKG;WACW,uBAAuB,CAAC,UAAU,EAAE,MAAM,GAAG,MAAM;IA2BjE;;;;;;;;;;OAUG;WACW,aAAa,CAAC,UAAU,EAAE,MAAM,GAAG,eAAe;IAKhE;;;;;;;;;OASG;WACW,QAAQ,CAAC,iBAAiB,EAAE,MAAM,GAAG,eAAe;IAOlE;;;;;;;;;OASG;WACW,cAAc,CAAC,UAAU,EAAE,OAAO,GAAG,eAAe;IAYlE;;;;;;;OAOG;WACW,cAAc,CAAC,aAAa,EAAE,kBAAkB,GAAG,eAAe;IA4BhF;;OAEG;IACI,QAAQ,CAAC,YAAY,EAAE,MAAM,GAAG,IAAI;IAM3C;;OAEG;IACI,YAAY,IAAI,OAAO;IAkC9B,OAAO,CAAC,MAAM,CAAC,uBAAuB;IA0BtC;;;;;;OAMG;IACI,eAAe,IAAI,MAAM;IAgChC;;;;;;;OAOG;IACI,eAAe,CAAC,aAAa,EAAE,kBAAkB,GAAG,IAAI;IAY/D;;;;;;OAMG;IACI,YAAY,CAAC,aAAa,EAAE,kBAAkB,GAAG,IAAI;IAsC5D,OAAO,CAAC,6BAA6B;CAqBtC"}