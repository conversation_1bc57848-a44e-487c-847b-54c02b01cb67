{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../src/index.ts"], "names": [], "mappings": ";AAAA,4FAA4F;AAC5F,2DAA2D;;;AAE3D;;;;;;;GAOG;AAEH,6DAA4D;AAAnD,oHAAA,gBAAgB,OAAA;AACzB,iDAAgD;AAAvC,wGAAA,UAAU,OAAA;AAEnB,QAAQ;AACR,2DAAwF;AAAjD,kHAAA,eAAe,OAAA;AACtD,+DAA8F;AAArD,sHAAA,iBAAiB,OAAA;AAC1D,2CAAuG;AAA9F,sGAAA,WAAW,OAAA;AAAwB,kGAAA,OAAO,OAAA;AACnD,2DAAwF;AAAjD,kHAAA,eAAe,OAAA;AAEtD,SAAS;AACT,wEAIwC;AADtC,8HAAA,qBAAqB,OAAA;AAEvB,gFAI4C;AAD1C,sIAAA,yBAAyB,OAAA;AAE3B,8DAA4F;AAApD,oHAAA,gBAAgB,OAAA;AACxD,wEAA2G;AAA9D,8HAAA,qBAAqB,OAAA;AAClE,gEAA+F;AAAtD,sHAAA,iBAAiB,OAAA;AAC1D,kEAAkG;AAAxD,wHAAA,kBAAkB,OAAA;AAC5D,kEAAkG;AAAxD,wHAAA,kBAAkB,OAAA;AAC5D,0DAAsF;AAAhD,gHAAA,cAAc,OAAA;AACpD,sDAAgF;AAA5C,4GAAA,YAAY,OAAA;AAChD,8DAA4F;AAApD,oHAAA,gBAAgB,OAAA;AACxD,8DAA4F;AAApD,oHAAA,gBAAgB,OAAA;AACxD,oEAAqG;AAA1D,0HAAA,mBAAmB,OAAA;AAC9D,8DAA4F;AAApD,oHAAA,gBAAgB,OAAA;AACxD,oEAIsC;AADpC,4HAAA,qBAAqB,OAAA;AAGvB,4CAM0B;AALxB,2GAAA,gBAAgB,OAAA;AAGhB,uGAAA,YAAY,OAAA;AACZ,kGAAA,OAAO,OAAA;AAIT,QAAQ;AACR,6DAA2F;AAAnD,oHAAA,gBAAgB,OAAA;AACxD,6CAAmE;AAAnC,oGAAA,QAAQ,OAAA;AACxC,yDAAqF;AAA/C,gHAAA,cAAc,OAAA;AACpD,uEAA0G;AAA7D,8HAAA,qBAAqB,OAAA;AAClE,uDAAkF;AAA7C,8GAAA,aAAa,OAAA;AAClD,2CAAgE;AAAjC,kGAAA,OAAO,OAAA;AACtC,uDAAmG;AAA9D,8GAAA,aAAa,OAAA;AAAE,gHAAA,eAAe,OAAA;AACnE,mDAA4E;AAAzC,0GAAA,WAAW,OAAA;AAC9C,+DAA8F;AAArD,sHAAA,iBAAiB,OAAA;AAC1D,qDAA+E;AAA3C,4GAAA,YAAY,OAAA;AAChD,+CAAsE;AAArC,sGAAA,SAAS,OAAA;AAC1C,iEAAiG;AAAvD,wHAAA,kBAAkB,OAAA;AAC5D,6CAA4C;AAAnC,oGAAA,QAAQ,OAAA;AACjB,qDAA+E;AAA3C,4GAAA,YAAY,OAAA;AAChD,iDAAsG;AAApE,wGAAA,UAAU,OAAA;AAC5C,+CAAsE;AAArC,sGAAA,SAAS,OAAA;AAC1C,mDAA4E;AAAzC,0GAAA,WAAW,OAAA;AAC9C,qEAAuG;AAA3D,4HAAA,oBAAoB,OAAA;AAChE,qDAA+E;AAA3C,4GAAA,YAAY,OAAA;AAChD,uDAAkF;AAA7C,8GAAA,aAAa,OAAA;AAClD,mDAA4E;AAAzC,0GAAA,WAAW,OAAA;AAE9C,qDAAoD;AAA3C,4GAAA,YAAY,OAAA;AACrB,yDAAqF;AAA/C,gHAAA,cAAc,OAAA", "sourcesContent": ["// Copyright (c) Microsoft Corporation. All rights reserved. Licensed under the MIT license.\n// See LICEN<PERSON> in the project root for license information.\n\n/**\n * Use this library to read and write *.api.json files as defined by the\n * {@link https://api-extractor.com/ | API Extractor}  tool.  These files are used to generate a documentation\n * website for your TypeScript package.  The files store the API signatures and doc comments that were extracted\n * from your package.\n *\n * @packageDocumentation\n */\n\nexport { AedocDefinitions } from './aedoc/AedocDefinitions';\nexport { ReleaseTag } from './aedoc/ReleaseTag';\n\n// items\nexport { type IApiDeclaredItemOptions, ApiDeclaredItem } from './items/ApiDeclaredItem';\nexport { type IApiDocumentedItemOptions, ApiDocumentedItem } from './items/ApiDocumentedItem';\nexport { ApiItemKind, type IApiItemOptions, ApiItem, type IApiItemConstructor } from './items/ApiItem';\nexport { type IApiPropertyItemOptions, ApiPropertyItem } from './items/ApiPropertyItem';\n\n// mixins\nexport {\n  type IApiParameterListMixinOptions,\n  type IApiParameterOptions,\n  ApiParameterListMixin\n} from './mixins/ApiParameterListMixin';\nexport {\n  type IApiTypeParameterOptions,\n  type IApiTypeParameterListMixinOptions,\n  ApiTypeParameterListMixin\n} from './mixins/ApiTypeParameterListMixin';\nexport { type IApiAbstractMixinOptions, ApiAbstractMixin } from './mixins/ApiAbstractMixin';\nexport { type IApiItemContainerMixinOptions, ApiItemContainerMixin } from './mixins/ApiItemContainerMixin';\nexport { type IApiProtectedMixinOptions, ApiProtectedMixin } from './mixins/ApiProtectedMixin';\nexport { type IApiReleaseTagMixinOptions, ApiReleaseTagMixin } from './mixins/ApiReleaseTagMixin';\nexport { type IApiReturnTypeMixinOptions, ApiReturnTypeMixin } from './mixins/ApiReturnTypeMixin';\nexport { type IApiStaticMixinOptions, ApiStaticMixin } from './mixins/ApiStaticMixin';\nexport { type IApiNameMixinOptions, ApiNameMixin } from './mixins/ApiNameMixin';\nexport { type IApiOptionalMixinOptions, ApiOptionalMixin } from './mixins/ApiOptionalMixin';\nexport { type IApiReadonlyMixinOptions, ApiReadonlyMixin } from './mixins/ApiReadonlyMixin';\nexport { type IApiInitializerMixinOptions, ApiInitializerMixin } from './mixins/ApiInitializerMixin';\nexport { type IApiExportedMixinOptions, ApiExportedMixin } from './mixins/ApiExportedMixin';\nexport {\n  type IFindApiItemsResult,\n  type IFindApiItemsMessage,\n  FindApiItemsMessageId\n} from './mixins/IFindApiItemsResult';\n\nexport {\n  ExcerptTokenKind,\n  type IExcerptTokenRange,\n  type IExcerptToken,\n  ExcerptToken,\n  Excerpt\n} from './mixins/Excerpt';\nexport type { Constructor, PropertiesOf } from './mixins/Mixin';\n\n// model\nexport { type IApiCallSignatureOptions, ApiCallSignature } from './model/ApiCallSignature';\nexport { type IApiClassOptions, ApiClass } from './model/ApiClass';\nexport { type IApiConstructorOptions, ApiConstructor } from './model/ApiConstructor';\nexport { type IApiConstructSignatureOptions, ApiConstructSignature } from './model/ApiConstructSignature';\nexport { type IApiEntryPointOptions, ApiEntryPoint } from './model/ApiEntryPoint';\nexport { type IApiEnumOptions, ApiEnum } from './model/ApiEnum';\nexport { type IApiEnumMemberOptions, ApiEnumMember, EnumMemberOrder } from './model/ApiEnumMember';\nexport { type IApiFunctionOptions, ApiFunction } from './model/ApiFunction';\nexport { type IApiIndexSignatureOptions, ApiIndexSignature } from './model/ApiIndexSignature';\nexport { type IApiInterfaceOptions, ApiInterface } from './model/ApiInterface';\nexport { type IApiMethodOptions, ApiMethod } from './model/ApiMethod';\nexport { type IApiMethodSignatureOptions, ApiMethodSignature } from './model/ApiMethodSignature';\nexport { ApiModel } from './model/ApiModel';\nexport { type IApiNamespaceOptions, ApiNamespace } from './model/ApiNamespace';\nexport { type IApiPackageOptions, ApiPackage, type IApiPackageSaveOptions } from './model/ApiPackage';\nexport { type IParameterOptions, Parameter } from './model/Parameter';\nexport { type IApiPropertyOptions, ApiProperty } from './model/ApiProperty';\nexport { type IApiPropertySignatureOptions, ApiPropertySignature } from './model/ApiPropertySignature';\nexport { type IApiTypeAliasOptions, ApiTypeAlias } from './model/ApiTypeAlias';\nexport { type ITypeParameterOptions, TypeParameter } from './model/TypeParameter';\nexport { type IApiVariableOptions, ApiVariable } from './model/ApiVariable';\nexport { type IResolveDeclarationReferenceResult } from './model/ModelReferenceResolver';\nexport { HeritageType } from './model/HeritageType';\nexport { type ISourceLocationOptions, SourceLocation } from './model/SourceLocation';\n"]}