{"version": 3, "file": "Parameter.js", "sourceRoot": "", "sources": ["../../src/model/Parameter.ts"], "names": [], "mappings": ";AAAA,4FAA4F;AAC5F,2DAA2D;;;AAI3D,kEAA+D;AAe/D;;;;;;;;;;;;;;;;GAgBG;AACH,MAAa,SAAS;IAkBpB,YAAmB,OAA0B;QAC3C,IAAI,CAAC,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC;QACzB,IAAI,CAAC,oBAAoB,GAAG,OAAO,CAAC,oBAAoB,CAAC;QACzD,IAAI,CAAC,UAAU,GAAG,OAAO,CAAC,UAAU,CAAC;QACrC,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC,MAAM,CAAC;IAChC,CAAC;IAED;;OAEG;IACH,IAAW,eAAe;QACxB,IAAI,IAAI,CAAC,OAAO,YAAY,qCAAiB,EAAE,CAAC;YAC9C,IAAI,IAAI,CAAC,OAAO,CAAC,YAAY,EAAE,CAAC;gBAC9B,OAAO,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,MAAM,CAAC,iBAAiB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACvE,CAAC;QACH,CAAC;IACH,CAAC;CACF;AAnCD,8BAmCC", "sourcesContent": ["// Copyright (c) Microsoft Corporation. All rights reserved. Licensed under the MIT license.\n// See LICENSE in the project root for license information.\n\nimport type * as tsdoc from '@microsoft/tsdoc';\n\nimport { ApiDocumentedItem } from '../items/ApiDocumentedItem';\nimport type { Excerpt } from '../mixins/Excerpt';\nimport type { ApiParameterListMixin } from '../mixins/ApiParameterListMixin';\n\n/**\n * Constructor options for {@link Parameter}.\n * @public\n */\nexport interface IParameterOptions {\n  name: string;\n  parameterTypeExcerpt: Excerpt;\n  isOptional: boolean;\n  parent: ApiParameterListMixin;\n}\n\n/**\n * Represents a named parameter for a function-like declaration.\n *\n * @remarks\n *\n * `Parameter` represents a TypeScript declaration such as `x: number` in this example:\n *\n * ```ts\n * export function add(x: number, y: number): number {\n *   return x + y;\n * }\n * ```\n *\n * `Parameter` objects belong to the {@link (ApiParameterListMixin:interface).parameters} collection.\n *\n * @public\n */\nexport class Parameter {\n  /**\n   * An {@link Excerpt} that describes the type of the parameter.\n   */\n  public readonly parameterTypeExcerpt: Excerpt;\n\n  /**\n   * The parameter name.\n   */\n  public name: string;\n\n  /**\n   * Whether the parameter is optional.\n   */\n  public isOptional: boolean;\n\n  private _parent: ApiParameterListMixin;\n\n  public constructor(options: IParameterOptions) {\n    this.name = options.name;\n    this.parameterTypeExcerpt = options.parameterTypeExcerpt;\n    this.isOptional = options.isOptional;\n    this._parent = options.parent;\n  }\n\n  /**\n   * Returns the `@param` documentation for this parameter, if present.\n   */\n  public get tsdocParamBlock(): tsdoc.DocParamBlock | undefined {\n    if (this._parent instanceof ApiDocumentedItem) {\n      if (this._parent.tsdocComment) {\n        return this._parent.tsdocComment.params.tryGetBlockByName(this.name);\n      }\n    }\n  }\n}\n"]}