{"version": 3, "file": "Mixin.js", "sourceRoot": "", "sources": ["../../src/mixins/Mixin.ts"], "names": [], "mappings": ";AAAA,4FAA4F;AAC5F,2DAA2D", "sourcesContent": ["// Copyright (c) Microsoft Corporation. All rights reserved. Licensed under the MIT license.\n// See LICENSE in the project root for license information.\n\n/**\n * This abstraction is used by the mixin pattern.\n * It describes a class constructor.\n * @public\n */\nexport type Constructor<T = {}> = new (...args: any[]) => T; // eslint-disable-line @typescript-eslint/no-explicit-any\n\n/**\n * This abstraction is used by the mixin pattern.\n * It describes the \"static side\" of a class.\n *\n * @public\n */\nexport type PropertiesOf<T> = { [K in keyof T]: T[K] };\n"]}