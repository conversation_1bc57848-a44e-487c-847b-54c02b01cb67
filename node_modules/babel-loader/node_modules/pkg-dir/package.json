{"name": "pkg-dir", "version": "7.0.0", "description": "Find the root directory of a Node.js project or npm package", "license": "MIT", "repository": "sindresorhus/pkg-dir", "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "type": "module", "exports": "./index.js", "engines": {"node": ">=14.16"}, "scripts": {"test": "xo && ava && tsd"}, "files": ["index.js", "index.d.ts"], "keywords": ["package", "json", "root", "npm", "entry", "find", "up", "find-up", "findup", "look-up", "look", "file", "search", "match", "resolve", "parent", "parents", "folder", "directory", "walk", "walking", "path"], "dependencies": {"find-up": "^6.3.0"}, "devDependencies": {"ava": "^4.3.1", "tempy": "^3.0.0", "tsd": "^0.22.0", "typescript": "^4.7.4", "xo": "^0.51.0"}}