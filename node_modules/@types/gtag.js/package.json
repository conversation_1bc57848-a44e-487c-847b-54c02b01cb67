{"name": "@types/gtag.js", "version": "0.0.12", "description": "TypeScript definitions for Google gtag.js API", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/gtag.js", "license": "MIT", "contributors": [{"name": " <PERSON><PERSON><PERSON>", "url": "https://github.com/rokt33r", "githubUsername": "rokt33r"}, {"name": "<PERSON>", "url": "https://github.com/KsAkira10", "githubUsername": "KsAkira10"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/gtag.js"}, "scripts": {}, "dependencies": {}, "typesPublisherContentHash": "1919d74395982c9beb3bb1bcbc26d9e298c9ee94bc454000c7a49d8333da0d28", "typeScriptVersion": "4.1"}